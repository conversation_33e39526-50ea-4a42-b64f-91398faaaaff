version: '3.8'

services:
  # MySQL 8.0 数据库
  mysql:
    image: mysql:8.0
    container_name: ruoyi-mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: root123
      MYSQL_DATABASE: ry-vue
      MYSQL_USER: ruoyi
      MYSQL_PASSWORD: ruoyi123
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./sql:/docker-entrypoint-initdb.d
    command: --default-authentication-plugin=mysql_native_password

  # Redis 3.0+ 缓存
  redis:
    image: redis:6.2-alpine
    container_name: ruoyi-redis
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes

  # RabbitMQ 3.6.2+ 消息队列
  rabbitmq:
    image: rabbitmq:3.9-management-alpine
    container_name: ruoyi-rabbitmq
    restart: always
    environment:
      RABBITMQ_DEFAULT_USER: admin
      RABBITMQ_DEFAULT_PASS: admin123
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq

  # 前端应用 (Nginx 1.18.0)
  frontend:
    build: .
    container_name: ruoyi-frontend
    restart: always
    ports:
      - "80:80"
    depends_on:
      - mysql
      - redis
      - rabbitmq
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf

volumes:
  mysql_data:
  redis_data:
  rabbitmq_data:
