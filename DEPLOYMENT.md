# 若依管理系统部署指南

## 环境准备

### 1. 系统要求检查
在开始部署前，请确保您的系统满足以下要求：

| 组件 | 版本要求 | 说明 |
|------|----------|------|
| Windows | Windows Server 2012 及以上 | 操作系统 |
| JDK | 1.8.0 | Java开发环境 |
| NodeJS | 12.15.0 | 前端运行环境 |
| MySQL | 8.0 及以上 | 数据库 |
| Redis | 3.0 及以上 | 缓存数据库 |
| RabbitMQ | 3.6.2 及以上 | 消息队列 |
| Nginx | 1.18.0 | Web服务器 |

### 2. 环境检查
运行环境检查脚本：
```bash
npm run check-env
```

## 开发环境部署

### 1. 克隆项目
```bash
git clone https://gitee.com/ys-gitee/RuoYi-Vue3.git
cd RuoYi-Vue3
```

### 2. 安装依赖
```bash
# 使用npm
npm install --registry=https://registry.npmmirror.com

# 或使用pnpm (推荐)
pnpm install

# 或使用yarn
yarn install
```

### 3. 启动开发服务器
```bash
npm run dev
```

访问地址：http://localhost:80

## 生产环境部署

### 方式一：传统部署

#### 1. 构建项目
```bash
npm run build:prod
```

#### 2. 配置Nginx
将构建产物 `dist` 目录部署到Nginx服务器，参考 `nginx.conf` 配置文件。

#### 3. 启动服务
```bash
# 启动Nginx
nginx -s reload
```

### 方式二：Docker部署

#### 1. 构建镜像
```bash
docker build -t ruoyi-frontend .
```

#### 2. 使用Docker Compose
```bash
docker-compose up -d
```

这将启动以下服务：
- 前端应用 (端口: 80)
- MySQL 8.0 (端口: 3306)
- Redis (端口: 6379)
- RabbitMQ (端口: 5672, 管理界面: 15672)

## 配置说明

### 环境变量配置
- `.env.development` - 开发环境配置
- `.env.production` - 生产环境配置
- `.env.staging` - 测试环境配置

### 主要配置项
- `VITE_APP_BASE_API` - 后端API地址
- `VITE_APP_TITLE` - 应用标题
- `VITE_BUILD_COMPRESS` - 构建压缩配置

## 常见问题

### 1. Node.js版本不兼容
确保使用Node.js 12.15.0或更高版本：
```bash
node --version
```

### 2. 依赖安装失败
尝试清除缓存并重新安装：
```bash
npm cache clean --force
rm -rf node_modules
npm install
```

### 3. 构建失败
检查内存是否足够，可以增加Node.js内存限制：
```bash
export NODE_OPTIONS="--max-old-space-size=4096"
npm run build:prod
```

## 监控和维护

### 1. 日志查看
- Nginx访问日志：`/var/log/nginx/access.log`
- Nginx错误日志：`/var/log/nginx/error.log`

### 2. 性能监控
建议使用以下工具进行性能监控：
- PM2 (Node.js进程管理)
- Prometheus + Grafana (系统监控)
- ELK Stack (日志分析)

### 3. 备份策略
- 定期备份MySQL数据库
- 备份Redis数据
- 备份应用配置文件

## 安全建议

1. 定期更新系统和软件版本
2. 配置防火墙规则
3. 使用HTTPS协议
4. 定期更换数据库密码
5. 启用访问日志监控
