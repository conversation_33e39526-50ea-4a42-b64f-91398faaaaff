#!/usr/bin/env node

/**
 * 环境检查脚本
 * 检查系统是否满足若依管理系统的运行要求
 */

const { execSync } = require('child_process');
const semver = require('semver');

// 系统要求配置
const REQUIREMENTS = {
  node: '12.15.0',
  npm: '6.0.0'
};

// 颜色输出
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function checkVersion(command, requirement, name) {
  try {
    const version = execSync(command, { encoding: 'utf8' }).trim();
    const cleanVersion = version.replace(/^v/, '');
    
    if (semver.gte(cleanVersion, requirement)) {
      log(`✓ ${name}: ${version} (要求: >=${requirement})`, 'green');
      return true;
    } else {
      log(`✗ ${name}: ${version} (要求: >=${requirement})`, 'red');
      return false;
    }
  } catch (error) {
    log(`✗ ${name}: 未安装 (要求: >=${requirement})`, 'red');
    return false;
  }
}

function checkOptionalTool(command, name) {
  try {
    const version = execSync(command, { encoding: 'utf8' }).trim();
    log(`✓ ${name}: ${version}`, 'green');
    return true;
  } catch (error) {
    log(`- ${name}: 未安装 (可选)`, 'yellow');
    return false;
  }
}

function main() {
  log('若依管理系统环境检查', 'blue');
  log('='.repeat(50), 'blue');
  
  let allPassed = true;
  
  // 检查必需的工具
  log('\n必需组件检查:', 'blue');
  allPassed &= checkVersion('node --version', REQUIREMENTS.node, 'Node.js');
  allPassed &= checkVersion('npm --version', REQUIREMENTS.npm, 'npm');
  
  // 检查可选工具
  log('\n可选组件检查:', 'blue');
  checkOptionalTool('yarn --version', 'Yarn');
  checkOptionalTool('pnpm --version', 'pnpm');
  checkOptionalTool('git --version', 'Git');
  
  // 检查后端服务（如果可用）
  log('\n后端服务检查:', 'blue');
  checkOptionalTool('java -version 2>&1 | head -1', 'Java');
  checkOptionalTool('mysql --version', 'MySQL');
  checkOptionalTool('redis-server --version', 'Redis');
  
  // 总结
  log('\n' + '='.repeat(50), 'blue');
  if (allPassed) {
    log('✓ 环境检查通过！可以开始开发。', 'green');
    process.exit(0);
  } else {
    log('✗ 环境检查失败！请安装缺失的组件。', 'red');
    log('\n安装建议:', 'yellow');
    log('1. 安装 Node.js: https://nodejs.org/', 'yellow');
    log('2. 更新 npm: npm install -g npm@latest', 'yellow');
    log('3. 参考 SYSTEM_REQUIREMENTS.md 了解详细要求', 'yellow');
    process.exit(1);
  }
}

main();
