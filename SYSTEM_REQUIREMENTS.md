# 系统要求以及所需软件版本

## 系统概述
若依管理系统 v3.8.7 - 基于SpringBoot+Vue3前后端分离的Java快速开发框架

## 系统要求及软件版本

| 组件 | 版本 |
|------|------|
| Windows | Windows Server 2012 及以上 |
| RabbitMQ | 版本 3.6.2 及以上 |
| Redis | 版本 3.0 及以上 |
| JDK | 版本 1.8.0 |
| NodeJS | 版本 12.15.0 |
| Nginx | 版本 1.18.0 |
| MySQL | 版本 8.0 及以上 |

## 前端技术栈
- Vue 3.4.0
- Element Plus 2.4.3
- Vite 5.0.4
- Pinia 2.1.7
- Vue Router 4.2.5

## 开发环境配置

### Node.js 环境
推荐使用 Node.js 12.15.0 或更高版本

### 包管理器
支持以下包管理器：
- npm
- yarn  
- pnpm

### 开发工具
- VS Code (推荐)
- WebStorm
- 其他支持 Vue.js 开发的 IDE

## 部署要求

### 服务器环境
- 操作系统：Windows Server 2012 及以上
- Web服务器：Nginx 1.18.0
- 应用服务器：支持 Java 1.8.0 的应用服务器

### 数据库
- MySQL 8.0 及以上版本
- Redis 3.0 及以上版本

### 消息队列
- RabbitMQ 3.6.2 及以上版本

## 端口配置
- 前端开发服务器：80 (可配置)
- 后端API服务器：8080 (可配置)
- MySQL：3306 (默认)
- Redis：6379 (默认)
- RabbitMQ：5672 (默认)

## 浏览器兼容性
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## 注意事项
1. 确保所有组件版本符合要求，避免兼容性问题
2. 生产环境建议使用更高版本的软件以获得更好的性能和安全性
3. 开发环境可以使用相对较新的版本进行开发和测试
